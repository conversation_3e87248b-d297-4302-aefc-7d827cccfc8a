// ===== SEARCH FUNCTIONALITY =====

// Search data structure
const searchData = {
    services: [
        {
            title: "Business Cards",
            description: "Professional business cards with premium quality printing",
            category: "Business Printing",
            icon: "fas fa-id-card",
            keywords: ["business", "cards", "professional", "networking", "contact"],
            action: () => openWhatsAppWithProduct("Business Cards")
        },
        {
            title: "Brochures",
            description: "High-quality brochures for marketing and promotional materials",
            category: "Marketing Materials",
            icon: "fas fa-book-open",
            keywords: ["brochure", "marketing", "promotional", "catalog", "leaflet"],
            action: () => openWhatsAppWithProduct("Brochures")
        },
        {
            title: "Banners",
            description: "Large format banners for events, advertising, and displays",
            category: "Large Format",
            icon: "fas fa-flag",
            keywords: ["banner", "large", "format", "display", "advertising", "event"],
            action: () => openWhatsAppWithProduct("Banners")
        },
        {
            title: "Custom Packaging",
            description: "Custom packaging solutions for products and gifts",
            category: "Packaging",
            icon: "fas fa-box",
            keywords: ["packaging", "custom", "box", "gift", "product", "wrapping"],
            action: () => openWhatsAppWithProduct("Custom Packaging")
        },
        {
            title: "Corporate Gifts",
            description: "Branded corporate gifts and promotional items",
            category: "Corporate",
            icon: "fas fa-gift",
            keywords: ["corporate", "gifts", "promotional", "branded", "items", "company"],
            action: () => openWhatsAppWithProduct("Corporate Gifts")
        },
        {
            title: "Flyers",
            description: "Eye-catching flyers for events and promotions",
            category: "Marketing Materials",
            icon: "fas fa-file-alt",
            keywords: ["flyer", "leaflet", "promotion", "event", "marketing"],
            action: () => openWhatsAppWithProduct("Flyers")
        },
        {
            title: "Stickers & Labels",
            description: "Custom stickers and labels for various applications",
            category: "Packaging",
            icon: "fas fa-tags",
            keywords: ["sticker", "label", "custom", "adhesive", "branding"],
            action: () => openWhatsAppWithProduct("Stickers & Labels")
        },
        {
            title: "Posters",
            description: "High-quality posters for advertising and decoration",
            category: "Large Format",
            icon: "fas fa-image",
            keywords: ["poster", "print", "advertising", "decoration", "wall"],
            action: () => openWhatsAppWithProduct("Posters")
        },
        {
            title: "Letterheads",
            description: "Professional letterheads for business correspondence",
            category: "Business Printing",
            icon: "fas fa-file-text",
            keywords: ["letterhead", "business", "correspondence", "professional", "header"],
            action: () => openWhatsAppWithProduct("Letterheads")
        },
        {
            title: "Envelopes",
            description: "Custom printed envelopes for business and personal use",
            category: "Business Printing",
            icon: "fas fa-envelope",
            keywords: ["envelope", "custom", "business", "mail", "correspondence"],
            action: () => openWhatsAppWithProduct("Envelopes")
        }
    ],
    pages: [
        {
            title: "Home",
            description: "Main page with overview of our printing services",
            category: "Navigation",
            icon: "fas fa-home",
            keywords: ["home", "main", "overview", "services"],
            action: () => scrollToSection('#home')
        },
        {
            title: "Services",
            description: "Complete list of our printing and design services",
            category: "Navigation",
            icon: "fas fa-cogs",
            keywords: ["services", "printing", "design", "offerings"],
            action: () => scrollToSection('#services')
        },
        {
            title: "About Us",
            description: "Learn more about InkImpressionAdv and our team",
            category: "Navigation",
            icon: "fas fa-info-circle",
            keywords: ["about", "company", "team", "information"],
            action: () => scrollToSection('#about')
        },
        {
            title: "Portfolio",
            description: "View our previous work and completed projects",
            category: "Navigation",
            icon: "fas fa-folder-open",
            keywords: ["portfolio", "work", "projects", "gallery", "examples"],
            action: () => scrollToSection('#portfolio')
        },
        {
            title: "Contact",
            description: "Get in touch with us for quotes and inquiries",
            category: "Navigation",
            icon: "fas fa-phone",
            keywords: ["contact", "phone", "email", "address", "location"],
            action: () => scrollToSection('#contact')
        }
    ]
};

// Search functionality class
class SearchManager {
    constructor() {
        this.searchInputs = document.querySelectorAll('#searchInput, #mobileSearchInput');
        this.searchButtons = document.querySelectorAll('#searchBtn, #mobileSearchBtn');
        this.searchResults = document.querySelectorAll('#searchResults, #mobileSearchResults');
        this.isSearchActive = false;

        this.init();
    }

    init() {
        // Add event listeners for search inputs
        this.searchInputs.forEach((input, index) => {
            input.addEventListener('input', (e) => this.handleSearch(e, index));
            input.addEventListener('focus', (e) => this.handleFocus(e, index));
            input.addEventListener('blur', (e) => this.handleBlur(e, index));
            input.addEventListener('keydown', (e) => this.handleKeydown(e, index));
        });

        // Add event listeners for search buttons
        this.searchButtons.forEach((button, index) => {
            button.addEventListener('click', (e) => this.handleSearchClick(e, index));
        });

        // Close search results when clicking outside
        document.addEventListener('click', (e) => this.handleOutsideClick(e));
    }

    handleSearch(event, index) {
        const query = event.target.value.trim();
        const resultsContainer = this.searchResults[index];

        if (query.length === 0) {
            this.hideResults(index);
            return;
        }

        if (query.length < 2) {
            return; // Wait for at least 2 characters
        }

        const results = this.performSearch(query);
        this.displayResults(results, index);
    }

    performSearch(query) {
        const searchTerm = query.toLowerCase();
        const allItems = [...searchData.services, ...searchData.pages];

        return allItems.filter(item => {
            // Search in title
            if (item.title.toLowerCase().includes(searchTerm)) return true;

            // Search in description
            if (item.description.toLowerCase().includes(searchTerm)) return true;

            // Search in keywords
            if (item.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm))) return true;

            // Search in category
            if (item.category.toLowerCase().includes(searchTerm)) return true;

            return false;
        }).sort((a, b) => {
            // Prioritize exact title matches
            const aExact = a.title.toLowerCase().includes(searchTerm);
            const bExact = b.title.toLowerCase().includes(searchTerm);

            if (aExact && !bExact) return -1;
            if (!aExact && bExact) return 1;

            return 0;
        });
    }

    displayResults(results, index) {
        const resultsContainer = this.searchResults[index];

        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="search-no-results">
                    <i class="fas fa-search"></i>
                    <p>No results found. Try different keywords.</p>
                </div>
            `;
        } else {
            // Group results by category
            const groupedResults = this.groupByCategory(results);
            resultsContainer.innerHTML = this.renderGroupedResults(groupedResults);

            // Add click event listeners to result items
            this.addResultClickListeners(resultsContainer, index);
        }

        this.showResults(index);
    }

    addResultClickListeners(container, searchIndex) {
        const resultItems = container.querySelectorAll('.search-result-item');
        resultItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const actionData = item.getAttribute('data-action');
                const allItems = [...searchData.services, ...searchData.pages];
                const foundItem = allItems.find(i => i.title === actionData);

                if (foundItem && foundItem.action) {
                    foundItem.action();
                    this.hideResults(searchIndex);
                    this.clearSearch(searchIndex);
                }
            });
        });
    }

    clearSearch(index) {
        if (this.searchInputs[index]) {
            this.searchInputs[index].value = '';
            this.searchInputs[index].blur();
        }
    }

    groupByCategory(results) {
        const grouped = {};
        results.forEach(item => {
            if (!grouped[item.category]) {
                grouped[item.category] = [];
            }
            grouped[item.category].push(item);
        });
        return grouped;
    }

    renderGroupedResults(groupedResults) {
        let html = '';

        Object.keys(groupedResults).forEach(category => {
            html += `<div class="search-category-header">${category}</div>`;

            groupedResults[category].forEach(item => {
                html += `
                    <div class="search-result-item" data-action="${item.title}">
                        <div class="search-result-icon">
                            <i class="${item.icon}"></i>
                        </div>
                        <div class="search-result-content">
                            <div class="search-result-title">${item.title}</div>
                            <div class="search-result-description">${item.description}</div>
                        </div>
                    </div>
                `;
            });
        });

        return html;
    }

    showResults(index) {
        if (this.searchResults[index]) {
            this.searchResults[index].classList.add('active');
            this.isSearchActive = true;
        }
    }

    hideResults(index) {
        if (this.searchResults[index]) {
            this.searchResults[index].classList.remove('active');
        }
        this.isSearchActive = false;
    }

    hideAllResults() {
        this.searchResults.forEach((results, index) => {
            if (results) {
                results.classList.remove('active');
            }
        });
        this.isSearchActive = false;
    }

    handleFocus(event, index) {
        if (event.target.value.trim().length >= 2) {
            this.showResults(index);
        }
    }

    handleBlur(event, index) {
        // Delay hiding to allow clicking on results
        setTimeout(() => {
            if (!this.isSearchActive) {
                this.hideResults(index);
            }
        }, 200);
    }

    handleKeydown(event, index) {
        if (event.key === 'Enter') {
            event.preventDefault();
            this.handleSearchClick(event, index);
        }

        if (event.key === 'Escape') {
            this.hideResults(index);
            event.target.blur();
        }
    }

    handleSearchClick(event, index) {
        const input = this.searchInputs[index];
        const query = input.value.trim();

        if (query.length >= 2) {
            const results = this.performSearch(query);
            if (results.length > 0) {
                // Execute the first result's action
                this.executeAction(results[0]);
                this.hideResults(index);
                input.blur();
            }
        }
    }

    handleOutsideClick(event) {
        const isSearchElement = Array.from(this.searchInputs).some(input =>
            input && (input.contains(event.target) || input === event.target)
        ) || Array.from(this.searchResults).some(results =>
            results && (results.contains(event.target) || results === event.target)
        ) || Array.from(this.searchButtons).some(button =>
            button && (button.contains(event.target) || button === event.target)
        );

        if (!isSearchElement) {
            this.hideAllResults();
        }
    }

    executeAction(item) {
        if (item.action) {
            item.action();
        }
    }
}

// Helper functions
function scrollToSection(selector) {
    const element = document.querySelector(selector);
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
    }
}

function openWhatsAppWithProduct(productName) {
    const message = `Hello! I'm interested in ${productName}. Can you provide more details and pricing?`;
    const phoneNumber = '971566786201';
    const whatsappURL = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappURL, '_blank');
}

// Initialize search functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new SearchManager();
});
