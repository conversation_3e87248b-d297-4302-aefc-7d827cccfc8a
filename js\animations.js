// ===== ADVANCED ANIMATIONS =====

document.addEventListener('DOMContentLoaded', function() {
    initGSAPAnimations();
    initScrollTriggerAnimations();
    initHoverAnimations();
    initLoadingAnimations();
    initCounterAnimations();
    initParallaxEffects();
});

// ===== GSAP ANIMATIONS =====
function initGSAPAnimations() {
    // Check if GSAP is loaded
    if (typeof gsap === 'undefined') {
        console.warn('GSAP not loaded, skipping GSAP animations');
        return;
    }

    // Hero section animations
    gsap.timeline()
        .from('.hero-title', {
            duration: 1,
            y: 50,
            opacity: 0,
            ease: 'power3.out'
        })
        .from('.hero-subtitle', {
            duration: 0.8,
            y: 30,
            opacity: 0,
            ease: 'power2.out'
        }, '-=0.5')
        .from('.hero-buttons', {
            duration: 0.8,
            y: 30,
            opacity: 0,
            ease: 'power2.out'
        }, '-=0.3')
        .from('.hero-features .feature-item', {
            duration: 0.6,
            y: 20,
            opacity: 0,
            stagger: 0.2,
            ease: 'power2.out'
        }, '-=0.2');

    // Logo animation
    gsap.from('.logo', {
        duration: 1,
        scale: 0.8,
        opacity: 0,
        ease: 'back.out(1.7)'
    });

    // Navigation menu animation
    gsap.from('.nav-menu li', {
        duration: 0.6,
        y: -20,
        opacity: 0,
        stagger: 0.1,
        ease: 'power2.out',
        delay: 0.5
    });
}

// ===== SCROLL TRIGGER ANIMATIONS =====
function initScrollTriggerAnimations() {
    if (typeof gsap === 'undefined' || typeof ScrollTrigger === 'undefined') {
        return;
    }

    gsap.registerPlugin(ScrollTrigger);

    // Service cards animation
    gsap.from('.service-card', {
        duration: 0.8,
        y: 50,
        opacity: 0,
        stagger: 0.2,
        ease: 'power2.out',
        scrollTrigger: {
            trigger: '.services-overview',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
        }
    });

    // Features animation
    gsap.from('.feature-item', {
        duration: 0.8,
        scale: 0.8,
        opacity: 0,
        stagger: 0.15,
        ease: 'back.out(1.7)',
        scrollTrigger: {
            trigger: '.why-choose-us',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
        }
    });

    // Portfolio items animation
    gsap.from('.portfolio-item', {
        duration: 0.8,
        y: 30,
        opacity: 0,
        stagger: 0.1,
        ease: 'power2.out',
        scrollTrigger: {
            trigger: '.portfolio',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
        }
    });

    // About section animation
    gsap.timeline({
        scrollTrigger: {
            trigger: '.about',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
        }
    })
    .from('.about-text', {
        duration: 0.8,
        x: -50,
        opacity: 0,
        ease: 'power2.out'
    })
    .from('.about-image', {
        duration: 0.8,
        x: 50,
        opacity: 0,
        ease: 'power2.out'
    }, '-=0.4')
    .from('.stat-item', {
        duration: 0.6,
        y: 20,
        opacity: 0,
        stagger: 0.2,
        ease: 'power2.out'
    }, '-=0.2');

    // Contact section animation
    gsap.timeline({
        scrollTrigger: {
            trigger: '.contact',
            start: 'top 80%',
            toggleActions: 'play none none reverse'
        }
    })
    .from('.contact-info', {
        duration: 0.8,
        x: -30,
        opacity: 0,
        ease: 'power2.out'
    })
    .from('.contact-form', {
        duration: 0.8,
        x: 30,
        opacity: 0,
        ease: 'power2.out'
    }, '-=0.4');
}

// ===== HOVER ANIMATIONS =====
function initHoverAnimations() {
    // Service card hover effects
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            if (typeof gsap !== 'undefined') {
                gsap.to(this, {
                    duration: 0.3,
                    y: -10,
                    scale: 1.02,
                    ease: 'power2.out'
                });
                
                gsap.to(this.querySelector('.service-icon'), {
                    duration: 0.3,
                    rotation: 5,
                    scale: 1.1,
                    ease: 'power2.out'
                });
            }
        });

        card.addEventListener('mouseleave', function() {
            if (typeof gsap !== 'undefined') {
                gsap.to(this, {
                    duration: 0.3,
                    y: 0,
                    scale: 1,
                    ease: 'power2.out'
                });
                
                gsap.to(this.querySelector('.service-icon'), {
                    duration: 0.3,
                    rotation: 0,
                    scale: 1,
                    ease: 'power2.out'
                });
            }
        });
    });

    // Button hover effects
    const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            if (typeof gsap !== 'undefined') {
                gsap.to(this, {
                    duration: 0.2,
                    scale: 1.05,
                    ease: 'power2.out'
                });
            }
        });

        button.addEventListener('mouseleave', function() {
            if (typeof gsap !== 'undefined') {
                gsap.to(this, {
                    duration: 0.2,
                    scale: 1,
                    ease: 'power2.out'
                });
            }
        });
    });
}

// ===== LOADING ANIMATIONS =====
function initLoadingAnimations() {
    // Page load animation
    window.addEventListener('load', function() {
        const loader = document.createElement('div');
        loader.className = 'page-loader';
        loader.innerHTML = `
            <div class="loader-content">
                <div class="loader-logo">InkImpressionAdv</div>
                <div class="loader-spinner"></div>
            </div>
        `;
        
        document.body.appendChild(loader);
        
        setTimeout(() => {
            if (typeof gsap !== 'undefined') {
                gsap.to(loader, {
                    duration: 0.5,
                    opacity: 0,
                    ease: 'power2.out',
                    onComplete: () => {
                        document.body.removeChild(loader);
                    }
                });
            } else {
                loader.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(loader);
                }, 500);
            }
        }, 1500);
    });
}

// ===== COUNTER ANIMATIONS =====
function initCounterAnimations() {
    const counters = document.querySelectorAll('.stat-item h3');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
        const duration = 2000;
        const increment = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            const suffix = counter.textContent.includes('+') ? '+' : '';
            counter.textContent = Math.floor(current).toLocaleString() + suffix;
        }, 16);
    };

    // Intersection Observer for counter animation
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => {
        observer.observe(counter);
    });
}

// ===== PARALLAX EFFECTS =====
function initParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.hero-background');
    
    window.addEventListener('scroll', throttle(() => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        
        parallaxElements.forEach(element => {
            element.style.transform = `translateY(${rate}px)`;
        });
    }, 16));
}

// ===== UTILITY FUNCTIONS =====
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// ===== CUSTOM ANIMATION CLASSES =====
function addCustomAnimationClasses() {
    const elements = document.querySelectorAll('[data-animate]');
    
    elements.forEach(element => {
        const animationType = element.getAttribute('data-animate');
        const delay = element.getAttribute('data-delay') || 0;
        
        setTimeout(() => {
            element.classList.add(animationType);
        }, delay);
    });
}

// ===== LOADING ANIMATION STYLES =====
const loaderStyles = `
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease;
}

.loader-content {
    text-align: center;
    color: white;
}

.loader-logo {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease;
}

.loader-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
`;

// Inject loader styles
const loaderStyleSheet = document.createElement('style');
loaderStyleSheet.textContent = loaderStyles;
document.head.appendChild(loaderStyleSheet);

// Initialize custom animation classes
document.addEventListener('DOMContentLoaded', addCustomAnimationClasses);
