// ===== WHATSAPP INTEGRATION =====

document.addEventListener('DOMContentLoaded', function() {
    initWhatsAppLinks();
    initProductInquiry();
    initQuickActions();
});

// ===== WHATSAPP LINK INITIALIZATION =====
function initWhatsAppLinks() {
    const whatsappLinks = document.querySelectorAll('.whatsapp-link');
    
    whatsappLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const productName = this.getAttribute('data-product');
            const message = createProductInquiryMessage(productName);
            const whatsappUrl = `https://wa.me/971566786201?text=${encodeURIComponent(message)}`;
            
            window.open(whatsappUrl, '_blank');
            
            // Track the click for analytics (optional)
            trackWhatsAppClick(productName);
        });
    });
}

// ===== PRODUCT INQUIRY HANDLING =====
function initProductInquiry() {
    // Handle mega menu product clicks
    const megaMenuLinks = document.querySelectorAll('.mega-menu a[data-product]');
    
    megaMenuLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const productName = this.getAttribute('data-product');
            const message = createProductInquiryMessage(productName);
            const whatsappUrl = `https://wa.me/971566786201?text=${encodeURIComponent(message)}`;
            
            window.open(whatsappUrl, '_blank');
            
            // Add visual feedback
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 200);
        });
    });
    
    // Handle footer product links
    const footerLinks = document.querySelectorAll('.footer a[data-product]');
    
    footerLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const productName = this.getAttribute('data-product');
            const message = createProductInquiryMessage(productName);
            const whatsappUrl = `https://wa.me/971566786201?text=${encodeURIComponent(message)}`;
            
            window.open(whatsappUrl, '_blank');
        });
    });
}

// ===== QUICK ACTIONS =====
function initQuickActions() {
    // Quick quote button
    const quickQuoteBtn = document.createElement('div');
    quickQuoteBtn.className = 'quick-quote-btn';
    quickQuoteBtn.innerHTML = `
        <button onclick="openQuickQuote()">
            <i class="fas fa-calculator"></i>
            Quick Quote
        </button>
    `;
    
    // Add quick actions to the page (optional)
    // document.body.appendChild(quickQuoteBtn);
}

// ===== MESSAGE CREATION FUNCTIONS =====

function createProductInquiryMessage(productName) {
    const currentTime = new Date().toLocaleString();
    
    return `Hello InkImpressionAdv! 👋

I'm interested in getting more information about your *${productName}* services.

Could you please provide me with:
• Pricing details
• Available options/specifications
• Turnaround time
• Any current offers or discounts

*Inquiry sent from your website on:* ${currentTime}

Thank you for your time! Looking forward to hearing from you.`;
}

function createGeneralInquiryMessage() {
    return `Hello InkImpressionAdv! 👋

I'm interested in your printing services and would like to know more about:

• Your service offerings
• Pricing information
• Turnaround times
• How to place an order

Please get back to me at your earliest convenience.

Thank you!`;
}

function createUrgentInquiryMessage(productName) {
    return `🚨 URGENT INQUIRY 🚨

Hello InkImpressionAdv!

I need *${productName}* services urgently. Could you please:

• Confirm if you can handle urgent orders
• Provide express pricing
• Let me know the fastest turnaround time possible

This is time-sensitive. Please respond ASAP.

Thank you!`;
}

function createBulkOrderMessage(productName, quantity) {
    return `📦 BULK ORDER INQUIRY

Hello InkImpressionAdv!

I'm interested in placing a bulk order for *${productName}*.

*Estimated Quantity:* ${quantity || 'To be discussed'}

Please provide:
• Bulk pricing discounts
• Minimum order quantities
• Delivery options for large orders
• Payment terms

Looking forward to a long-term business relationship.

Best regards!`;
}

// ===== UTILITY FUNCTIONS =====

function openQuickQuote() {
    const message = `Hello! I'd like to get a quick quote for printing services. 

Please let me know:
• Your most popular services
• Starting prices
• How to proceed with an order

Thank you!`;
    
    const whatsappUrl = `https://wa.me/971566786201?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function openUrgentInquiry(productName = 'printing services') {
    const message = createUrgentInquiryMessage(productName);
    const whatsappUrl = `https://wa.me/971566786201?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function openBulkInquiry(productName = 'printing services', quantity = '') {
    const message = createBulkOrderMessage(productName, quantity);
    const whatsappUrl = `https://wa.me/971566786201?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function trackWhatsAppClick(productName) {
    // Analytics tracking (implement based on your analytics platform)
    if (typeof gtag !== 'undefined') {
        gtag('event', 'whatsapp_click', {
            'product_name': productName,
            'event_category': 'engagement',
            'event_label': 'WhatsApp Inquiry'
        });
    }
    
    // Console log for debugging
    console.log(`WhatsApp inquiry initiated for: ${productName}`);
}

// ===== FLOATING WHATSAPP ENHANCEMENTS =====
function enhanceFloatingWhatsApp() {
    const floatingBtn = document.querySelector('.whatsapp-float a');
    
    if (floatingBtn) {
        // Add tooltip
        floatingBtn.setAttribute('title', 'Chat with us on WhatsApp!');
        
        // Add click tracking
        floatingBtn.addEventListener('click', function() {
            trackWhatsAppClick('Floating Button');
        });
        
        // Add notification badge (optional)
        const badge = document.createElement('span');
        badge.className = 'whatsapp-badge';
        badge.textContent = '1';
        floatingBtn.appendChild(badge);
        
        // Remove badge after first click
        floatingBtn.addEventListener('click', function() {
            if (badge.parentNode) {
                badge.remove();
            }
        }, { once: true });
    }
}

// ===== SMART SUGGESTIONS =====
function initSmartSuggestions() {
    const suggestions = [
        {
            trigger: 'business card',
            message: 'I see you\'re interested in business cards! We offer premium quality business cards with various finishes. Would you like to know about our most popular options?'
        },
        {
            trigger: 'banner',
            message: 'Looking for banners? We specialize in high-quality vinyl banners perfect for events and promotions. What size are you considering?'
        },
        {
            trigger: 'urgent',
            message: 'Need something urgently? We offer express services for most of our products. Let me know what you need and by when!'
        }
    ];
    
    // This could be expanded to show smart suggestions based on user behavior
}

// ===== ADDITIONAL STYLES FOR WHATSAPP FEATURES =====
const whatsappStyles = `
.whatsapp-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    animation: pulse 2s infinite;
}

.quick-quote-btn {
    position: fixed;
    bottom: 100px;
    left: 30px;
    z-index: 999;
}

.quick-quote-btn button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.quick-quote-btn button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.mega-menu a.clicked {
    background: rgba(37, 211, 102, 0.1);
    transform: scale(0.95);
}

@media (max-width: 768px) {
    .quick-quote-btn {
        bottom: 80px;
        left: 20px;
    }
    
    .quick-quote-btn button {
        padding: 10px 16px;
        font-size: 14px;
    }
}
`;

// Inject WhatsApp-specific styles
const whatsappStyleSheet = document.createElement('style');
whatsappStyleSheet.textContent = whatsappStyles;
document.head.appendChild(whatsappStyleSheet);

// Initialize enhancements when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    enhanceFloatingWhatsApp();
    initSmartSuggestions();
});

// ===== EXPORT FUNCTIONS FOR GLOBAL USE =====
window.WhatsAppIntegration = {
    openQuickQuote,
    openUrgentInquiry,
    openBulkInquiry,
    createProductInquiryMessage,
    createGeneralInquiryMessage
};
