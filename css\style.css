/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== ROYAL BLUE COLOR VARIABLES ===== */
:root {
    --royal-blue: #1E3A8A;        /* Royal Blue Primary */
    --royal-blue-dark: #1E40AF;   /* Dark Royal Blue */
    --royal-blue-light: #3B82F6;  /* Light Royal Blue */
    --royal-blue-bright: #60A5FA; /* Bright Royal Blue */
    --royal-blue-soft: #93C5FD;   /* Soft Royal Blue */
    --royal-blue-pale: #DBEAFE;   /* Pale Royal Blue */
    --royal-blue-accent: #2563EB; /* Accent Royal Blue */
    --navy-complement: #1E293B;   /* Navy Complement */
    --slate-complement: #334155;  /* Slate Complement */
    --gray-complement: #64748B;   /* Gray Complement */
    --light-gray: #F1F5F9;        /* Light Gray */
    --almost-white: #FAFBFC;      /* Almost White */

    /* Functional Colors */
    --text-primary: #1E293B;      /* Dark Text Primary */
    --text-secondary: #475569;    /* Medium Gray Text */
    --text-muted: #64748B;        /* Muted Gray Text */
    --text-light: #94A3B8;       /* Light Gray Text */
    --text-white: #FFFFFF;        /* White Text */
    --background-primary: #FFFFFF; /* White Background */
    --background-secondary: #F8FAFC; /* Light Gray Background */
    --background-light: #F1F5F9;  /* Very Light Background */
    --white: #FFFFFF;
    --whatsapp-green: #25D366;
    --whatsapp-dark: #128C7E;
    --light-accent: #E5EFFF;      /* Light Blue Accent */
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 1.8rem; }
h4 { font-size: 1.4rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.lead {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--text-primary);
}

/* ===== BUTTONS ===== */
.btn-primary, .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--royal-blue), var(--royal-blue-accent));
    color: var(--text-white);
    box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--royal-blue-accent), var(--royal-blue));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);
}

.btn-secondary {
    background: white;
    color: var(--royal-blue);
    border: 2px solid var(--royal-blue);
}

.btn-secondary:hover {
    background: var(--royal-blue);
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

/* ===== HEADER ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(30, 58, 138, 0.1);
}

.top-bar {
    background: var(--royal-blue);
    color: var(--text-white);
    padding: 8px 0;
    font-size: 0.9rem;
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex: 1;
}

.contact-info a,
.contact-info span {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.contact-link {
    color: var(--text-white);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
    white-space: nowrap;
}

.contact-link:hover {
    color: var(--light-accent);
}

.contact-hours {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-white);
    font-size: 0.9rem;
    white-space: nowrap;
}

.social-links {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
}

.social-links a {
    color: var(--text-white);
    font-size: 1.1rem;
    transition: color 0.3s ease;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.social-links a:hover {
    color: var(--light-accent);
}

/* ===== MAIN NAVIGATION ===== */
.main-nav {
    padding: 1rem 0;
    background: var(--white);
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-image {
    height: 50px;
    width: auto;
    object-fit: contain;
}

.logo-text h1 {
    font-size: 2rem;
    color: var(--royal-blue);
    margin: 0;
}

.logo-text span {
    font-size: 0.9rem;
    color: var(--royal-blue-light);
    font-weight: 400;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-menu a:hover {
    color: var(--royal-blue);
}

/* ===== MEGA MENU ===== */
.mega-menu-item {
    position: relative;
}

.mega-menu {
    position: absolute;
    top: 100%;
    left: -400px;
    width: 1200px;
    background: var(--white);
    box-shadow: 0 10px 40px rgba(30, 58, 138, 0.15);
    border-radius: 10px;
    padding: 2rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 1000;
    border: 1px solid var(--royal-blue-pale);
}

.mega-menu-item:hover .mega-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mega-menu-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

.mega-menu-column h3 {
    color: var(--royal-blue);
    font-size: 1.2rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--royal-blue-light);
}

.mega-menu-column ul {
    list-style: none;
}

.mega-menu-column li {
    margin-bottom: 0.5rem;
}

.mega-menu-column a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.95rem;
}

.mega-menu-column a:hover {
    color: var(--royal-blue);
}

/* ===== MOBILE MENU ===== */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    transition: all 0.3s ease;
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    color: white;
    margin-top: 120px;
    overflow: hidden;
}

/* ===== HERO SLIDER ===== */
.hero-slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.9) 0%, rgba(30, 64, 175, 0.8) 30%, rgba(59, 130, 246, 0.7) 70%, rgba(37, 99, 235, 0.8) 100%);
}

/* Individual slide backgrounds */
.slide-1 {
    background-image: url('https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80');
}

.slide-2 {
    background-image: url('https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80');
}

.slide-3 {
    background-image: url('https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80');
}

.slide-4 {
    background-image: url('https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80');
}

.slide-5 {
    background-image: url('https://images.unsplash.com/photo-1549298916-b41d501d3772?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80');
}

/* ===== SLIDER NAVIGATION ===== */
.slider-dots {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 3;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active,
.dot:hover {
    background: white;
    transform: scale(1.2);
}

.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 30px;
    z-index: 3;
}

.nav-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
}

.nav-btn:active {
    transform: scale(0.95);
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
}

.hero-title .highlight {
    color: var(--light-accent);
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    color: var(--text-white);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 3rem;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.feature-item i {
    font-size: 2rem;
    color: var(--light-accent);
}

.feature-item span {
    color: var(--text-white);
    font-weight: 500;
}

/* ===== SERVICES OVERVIEW ===== */
.services-overview {
    padding: 100px 0;
    background: var(--background-light);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--text-secondary);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 30px rgba(30, 58, 138, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    border: 1px solid var(--royal-blue-pale);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(30, 58, 138, 0.15);
}

.service-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.05);
}

.service-content {
    padding: 2rem;
    text-align: center;
}

.service-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--royal-blue), var(--royal-blue-accent));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    position: relative;
    top: -30px;
    box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

.service-icon i {
    font-size: 1.5rem;
    color: white;
}

.service-card h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    margin-top: -15px;
}

.service-card p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--royal-blue);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border: 2px solid var(--royal-blue);
    border-radius: 25px;
    background: transparent;
}

.service-link:hover {
    color: var(--text-white);
    background: var(--royal-blue);
    box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

/* ===== WHY CHOOSE US ===== */
.why-choose-us {
    padding: 100px 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 1.8rem;
    color: white;
}

.feature-item h3 {
    color: #1a1a1a;
    margin-bottom: 1rem;
}

/* ===== PORTFOLIO ===== */
.portfolio {
    padding: 100px 0;
    background: #f8f9fa;
}

.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.filter-btn {
    padding: 10px 20px;
    border: 2px solid #ddd;
    background: white;
    color: #666;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn.active,
.filter-btn:hover {
    background: #25D366;
    color: white;
    border-color: #25D366;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.portfolio-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-5px);
}

.portfolio-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.1);
}

.portfolio-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
}

.portfolio-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.portfolio-placeholder span {
    font-size: 1.2rem;
    font-weight: 600;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(37, 211, 102, 0.9);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    padding: 2rem;
    text-align: center;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-overlay h3 {
    margin-bottom: 0.5rem;
    color: white;
}

.portfolio-overlay p {
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.9);
}

.portfolio-link {
    color: white;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 8px 16px;
    border: 2px solid white;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.portfolio-link:hover {
    background: white;
    color: #25D366;
}

/* ===== ABOUT SECTION ===== */
.about {
    padding: 100px 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    color: #1a1a1a;
    margin-bottom: 1.5rem;
}

.about-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-item h3 {
    font-size: 2.5rem;
    color: #25D366;
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: #666;
    font-weight: 500;
}

.about-image {
    height: 400px;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
}

.image-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.image-placeholder span {
    font-size: 1.3rem;
    font-weight: 600;
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: 100px 0;
    background: var(--background-light);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 1.5rem;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(30, 58, 138, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(30, 58, 138, 0.12);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--royal-blue), var(--royal-blue-accent));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 4px 15px rgba(30, 58, 138, 0.2);
}

.contact-icon i {
    color: var(--text-white);
    font-size: 1.4rem;
}

.contact-details h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.contact-details p {
    color: var(--text-secondary);
    margin: 0;
    font-weight: 500;
}

.contact-form {
    background: var(--white);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(30, 58, 138, 0.1);
    border: 1px solid var(--royal-blue-pale);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid var(--royal-blue-pale);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: inherit;
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--royal-blue);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-muted);
}

/* ===== FOOTER ===== */
.footer {
    background: linear-gradient(135deg, var(--royal-blue) 0%, var(--royal-blue-dark) 50%, var(--navy-complement) 100%);
    color: var(--text-white);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
}

.footer-logo-image {
    height: 40px;
    width: auto;
    object-fit: contain;
    margin-bottom: 0.5rem;
}

.footer-logo h3 {
    color: var(--text-white);
    margin-bottom: 0.5rem;
}

.footer-logo p {
    color: var(--light-accent);
    margin-bottom: 1rem;
}

.footer-section h4 {
    color: var(--text-white);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: var(--royal-blue-soft);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--text-white);
}

.footer-section p {
    color: var(--royal-blue-soft);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-section i {
    color: var(--light-accent);
}

.footer-bottom {
    border-top: 1px solid var(--royal-blue-light);
    padding-top: 2rem;
    text-align: center;
}

.footer-bottom p {
    color: var(--royal-blue-soft);
    margin: 0;
}

/* ===== WHATSAPP FLOATING BUTTON ===== */
.whatsapp-float {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.whatsapp-float a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #25D366, #128C7E);
    border-radius: 50%;
    color: white;
    font-size: 1.8rem;
    text-decoration: none;
    box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.whatsapp-float a:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(37, 211, 102, 0.6);
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    }
    50% {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.6), 0 0 0 10px rgba(37, 211, 102, 0.1);
    }
    100% {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    }
}

/* ===== RESPONSIVE DESIGN ===== */

/* Extra Large Screens */
@media (max-width: 1400px) {
    .container {
        max-width: 1140px;
    }

    .mega-menu {
        width: 1000px;
        left: -300px;
    }

    .mega-menu-content {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Large Screens */
@media (max-width: 1200px) {
    .container {
        max-width: 960px;
    }

    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .hero-title {
        font-size: 3.5rem;
    }
}

/* Medium Screens */
@media (max-width: 1024px) {
    .container {
        max-width: 720px;
    }

    .mega-menu {
        width: 700px;
        left: -200px;
    }

    .mega-menu-content {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-features {
        gap: 2rem;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .contact-form {
        padding: 2.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .services-overview,
    .contact,
    .about {
        padding: 80px 0;
    }
}

/* Tablet Screens */
@media (max-width: 768px) {
    .container {
        max-width: 540px;
        padding: 0 20px;
    }

    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mega-menu {
        display: none;
    }

    .hero {
        margin-top: 100px;
        padding: 2rem 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .hero-buttons .btn-primary,
    .hero-buttons .btn-secondary {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .hero-features {
        flex-direction: column;
        gap: 1.5rem;
    }

    /* Slider responsive styles */
    .slider-nav {
        padding: 0 15px;
    }

    .nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .slider-dots {
        bottom: 20px;
    }

    .dot {
        width: 10px;
        height: 10px;
    }

    .services-grid,
    .features-grid,
    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card {
        margin: 0 auto;
        max-width: 400px;
    }

    .about-stats {
        justify-content: center;
        flex-wrap: wrap;
    }

    .contact-info {
        gap: 1.5rem;
    }

    .contact-item {
        padding: 1.25rem;
    }

    .contact-form {
        padding: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .top-bar-content {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
        padding: 0.5rem 0;
    }

    .contact-info {
        justify-content: center;
        gap: 1.5rem;
        flex-wrap: nowrap;
        flex-direction: row;
    }

    .social-links {
        justify-content: center;
        width: 100%;
    }

    .services-overview,
    .contact,
    .about {
        padding: 60px 0;
    }

    .section-header h2 {
        font-size: 2rem;
    }
}

/* Mobile Screens */
@media (max-width: 480px) {
    .container {
        padding: 0 15px;
        max-width: 100%;
    }

    h1 { font-size: 2rem; }
    h2 { font-size: 1.8rem; }
    h3 { font-size: 1.4rem; }

    .hero {
        padding: 1.5rem 0;
    }

    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-buttons .btn-primary,
    .hero-buttons .btn-secondary {
        padding: 12px 24px;
        font-size: 0.95rem;
    }

    /* Logo responsive styles */
    .logo {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .logo-image {
        height: 35px;
    }

    .logo-text h1 {
        font-size: 1.5rem;
    }

    .logo-text span {
        font-size: 0.8rem;
    }

    .footer-logo-image {
        height: 30px;
    }

    /* Services responsive */
    .services-grid {
        gap: 1rem;
    }

    .service-card {
        padding: 1.5rem;
        max-width: 100%;
    }

    .service-content {
        padding: 1.5rem;
    }

    .service-icon {
        width: 50px;
        height: 50px;
        top: -25px;
    }

    .service-icon i {
        font-size: 1.2rem;
    }

    /* Contact responsive */
    .contact-form {
        padding: 1.5rem;
    }

    .contact-item {
        padding: 1rem;
        gap: 1rem;
    }

    .contact-icon {
        width: 50px;
        height: 50px;
    }

    .contact-icon i {
        font-size: 1.2rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 12px 16px;
        font-size: 0.95rem;
    }

    .about-stats {
        flex-direction: column;
        gap: 1rem;
    }

    /* Portfolio responsive */
    .portfolio-filter {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    .whatsapp-float {
        bottom: 20px;
        right: 20px;
    }

    .whatsapp-float a {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .services-overview,
    .contact,
    .about {
        padding: 40px 0;
    }

    .section-header {
        margin-bottom: 2.5rem;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }

    .section-header p {
        font-size: 1rem;
    }

    /* Top bar mobile improvements */
    .top-bar {
        padding: 8px 0;
    }

    .top-bar-content {
        gap: 0.5rem;
    }

    .contact-info {
        gap: 1rem;
        flex-wrap: nowrap;
        justify-content: center;
        flex-direction: row;
    }

    .contact-link,
    .contact-hours {
        font-size: 0.85rem;
        text-align: center;
    }

    .social-links {
        gap: 0.75rem;
        justify-content: center;
    }

    .social-links a {
        font-size: 1rem;
    }
}

/* Extra Small Screens */
@media (max-width: 360px) {
    .container {
        padding: 0 10px;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .hero-buttons .btn-primary,
    .hero-buttons .btn-secondary {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .contact-form,
    .service-content {
        padding: 1.25rem;
    }

    .contact-item {
        padding: 0.75rem;
    }

    .whatsapp-float a {
        width: 45px;
        height: 45px;
        font-size: 1.3rem;
    }

    /* Top bar extra small screens */
    .top-bar {
        padding: 6px 0;
    }

    .top-bar-content {
        gap: 0.5rem;
        padding: 0.25rem 0;
    }

    .contact-info {
        gap: 0.75rem;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        flex-wrap: nowrap;
    }

    .contact-link,
    .contact-hours {
        font-size: 0.75rem;
        text-align: center;
    }

    .social-links {
        gap: 0.5rem;
        justify-content: center;
        width: 100%;
    }

    .social-links a {
        font-size: 0.9rem;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden { display: none; }
.visible { display: block; }

/* ===== ANIMATIONS ===== */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.slide-up {
    transform: translateY(30px);
    opacity: 0;
    animation: slideUp 0.6s ease forwards;
}

@keyframes slideUp {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.scale-in {
    transform: scale(0.9);
    opacity: 0;
    animation: scaleIn 0.6s ease forwards;
}

@keyframes scaleIn {
    to {
        transform: scale(1);
        opacity: 1;
    }
}
