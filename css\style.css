/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
}

/* ===== ROYAL BLUE COLOR VARIABLES ===== */
:root {
    --royal-blue: #1E3A8A;        /* Royal Blue Primary */
    --royal-blue-dark: #1E40AF;   /* Dark Royal Blue */
    --royal-blue-light: #3B82F6;  /* Light Royal Blue */
    --royal-blue-bright: #60A5FA; /* Bright Royal Blue */
    --royal-blue-soft: #93C5FD;   /* Soft Royal Blue */
    --royal-blue-pale: #DBEAFE;   /* Pale Royal Blue */
    --royal-blue-accent: #2563EB; /* Accent Royal Blue */
    --navy-complement: #1E293B;   /* Navy Complement */
    --slate-complement: #334155;  /* Slate Complement */
    --gray-complement: #64748B;   /* Gray Complement */
    --light-gray: #F1F5F9;        /* Light Gray */
    --almost-white: #FAFBFC;      /* Almost White */

    /* Functional Colors */
    --text-primary: #1E293B;      /* Dark Text Primary */
    --text-secondary: #475569;    /* Medium Gray Text */
    --text-muted: #64748B;        /* Muted Gray Text */
    --text-light: #94A3B8;       /* Light Gray Text */
    --text-white: #FFFFFF;        /* White Text */
    --background-primary: #FFFFFF; /* White Background */
    --background-secondary: #F8FAFC; /* Light Gray Background */
    --background-light: #F1F5F9;  /* Very Light Background */
    --white: #FFFFFF;
    --whatsapp-green: #25D366;
    --whatsapp-dark: #128C7E;
    --light-accent: #E5EFFF;      /* Light Blue Accent */
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 1.8rem; }
h4 { font-size: 1.4rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.lead {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--text-primary);
}

/* ===== BUTTONS ===== */
.btn-primary, .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 1rem;
    box-sizing: border-box;
    min-height: 44px;
    justify-content: center;
    white-space: nowrap;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, var(--royal-blue), var(--royal-blue-accent));
    color: var(--text-white);
    box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--royal-blue-accent), var(--royal-blue));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);
}

.btn-secondary {
    background: white;
    color: var(--royal-blue);
    border: 2px solid var(--royal-blue);
}

.btn-secondary:hover {
    background: var(--royal-blue);
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

/* ===== HEADER ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(30, 58, 138, 0.1);
}

.top-bar {
    background: var(--royal-blue);
    color: var(--text-white);
    padding: 10px 0;
    font-size: 0.9rem;
    min-height: 40px;
    display: block;
    width: 100%;
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.contact-info {
    display: flex !important;
    align-items: center;
    gap: 2rem;
    flex: 1;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
}

.contact-info a,
.contact-info span {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
}

.contact-link {
    color: var(--text-white);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
    white-space: nowrap;
}

.contact-link:hover {
    color: var(--light-accent);
}

.contact-hours {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-white);
    font-size: 0.9rem;
    white-space: nowrap;
}

.social-links {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
}

.social-links a {
    color: var(--text-white);
    font-size: 1.1rem;
    transition: color 0.3s ease;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.social-links a:hover {
    color: var(--light-accent);
}

/* ===== MAIN NAVIGATION ===== */
.main-nav {
    padding: 1rem 0;
    background: var(--white);
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-image {
    height: 50px;
    width: auto;
    object-fit: contain;
}

.logo-text h1 {
    font-size: 2rem;
    color: var(--royal-blue);
    margin: 0;
}

.logo-text span {
    font-size: 0.9rem;
    color: var(--royal-blue-light);
    font-weight: 400;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-menu a:hover {
    color: var(--royal-blue);
}

/* ===== MEGA MENU ===== */
.mega-menu-item {
    position: relative;
}

.mega-menu {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(20px);
    width: 100vw;
    max-width: 1200px;
    background: var(--white);
    box-shadow: 0 10px 40px rgba(30, 58, 138, 0.15);
    border-radius: 10px;
    padding: 2rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 9998;
    border: 1px solid var(--royal-blue-pale);
    margin: 0 20px;
}

.mega-menu-item:hover .mega-menu {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
}

.mega-menu-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

.mega-menu-column h3 {
    color: var(--royal-blue);
    font-size: 1.2rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--royal-blue-light);
}

.mega-menu-column ul {
    list-style: none;
}

.mega-menu-column li {
    margin-bottom: 0.5rem;
}

.mega-menu-column a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.95rem;
}

.mega-menu-column a:hover {
    color: var(--royal-blue);
}

/* ===== MOBILE MENU ===== */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
    z-index: 10001;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    transition: all 0.3s ease;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-menu {
    position: fixed;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100vh;
    background: var(--white);
    z-index: 10000;
    transition: left 0.3s ease;
    padding-top: 120px;
    overflow-y: auto;
}

.mobile-menu.active {
    left: 0;
}

.mobile-menu-content {
    padding: 2rem;
}

.mobile-nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.mobile-nav-menu li {
    margin-bottom: 1rem;
}

.mobile-nav-menu a {
    display: block;
    padding: 1rem 0;
    color: var(--text-primary);
    text-decoration: none;
    font-size: 1.2rem;
    font-weight: 500;
    border-bottom: 1px solid var(--royal-blue-pale);
    transition: color 0.3s ease;
}

.mobile-nav-menu a:hover {
    color: var(--royal-blue);
}

.mobile-services-menu {
    list-style: none;
    margin: 1rem 0;
    padding-left: 1rem;
    display: none;
}

.mobile-services-menu.active {
    display: block;
}

.mobile-services-menu li {
    margin-bottom: 0.5rem;
}

.mobile-services-menu a {
    font-size: 1rem;
    padding: 0.5rem 0;
    border-bottom: none;
    color: var(--text-secondary);
}

.mobile-services-toggle {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-services-toggle i {
    transition: transform 0.3s ease;
}

.mobile-services-toggle.active i {
    transform: rotate(180deg);
}

.mobile-menu-cta {
    margin-top: 2rem;
}

.mobile-cta-btn {
    width: 100%;
    justify-content: center;
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: calc(100vh - 130px);
    height: auto;
    display: flex;
    align-items: center;
    position: relative;
    color: white;
    margin-top: 130px;
    overflow: hidden;
    width: 100%;
    max-width: 100vw;
    padding: 3rem 0;
}

/* ===== HERO SLIDER ===== */
.hero-slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.9) 0%, rgba(30, 64, 175, 0.8) 30%, rgba(59, 130, 246, 0.7) 70%, rgba(37, 99, 235, 0.8) 100%);
}

/* Individual slide backgrounds */
.slide-1 {
    background-image: url('https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80');
}

.slide-2 {
    background-image: url('https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80');
}

.slide-3 {
    background-image: url('https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80');
}

.slide-4 {
    background-image: url('https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80');
}

.slide-5 {
    background-image: url('https://images.unsplash.com/photo-1549298916-b41d501d3772?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80');
}

/* ===== SLIDER NAVIGATION ===== */
.slider-dots {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 3;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active,
.dot:hover {
    background: white;
    transform: scale(1.2);
}

.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 30px;
    z-index: 3;
}

.nav-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
}

.nav-btn:active {
    transform: scale(0.95);
}

.hero-content {
    position: relative;
    z-index: 100;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.hero-title {
    font-size: clamp(2rem, 5vw, 4rem);
    margin-bottom: 1.5rem;
    font-weight: 700;
    line-height: 1.2;
    word-wrap: break-word;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7), 0 0 20px rgba(0, 0, 0, 0.5);
    max-width: 100%;
}

.hero-title .highlight {
    color: var(--light-accent);
}

.hero-subtitle {
    font-size: clamp(1rem, 2.5vw, 1.3rem);
    margin-bottom: 2rem;
    opacity: 0.95;
    color: var(--text-white);
    line-height: 1.5;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.6), 0 0 10px rgba(0, 0, 0, 0.4);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    width: 100%;
    max-width: 100%;
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 3rem;
    flex-wrap: wrap;
    width: 100%;
    max-width: 100%;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.feature-item i {
    font-size: 2rem;
    color: var(--light-accent);
}

.feature-item span {
    color: var(--text-white);
    font-weight: 500;
}

/* ===== SERVICES OVERVIEW ===== */
.services-overview {
    padding: 100px 0;
    background: var(--background-light);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--text-secondary);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(350px, 100%), 1fr));
    gap: 2rem;
    width: 100%;
    max-width: 100%;
}

.service-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 30px rgba(30, 58, 138, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    border: 1px solid var(--royal-blue-pale);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(30, 58, 138, 0.15);
}

.service-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.05);
}

.service-content {
    padding: 2rem;
    text-align: center;
}

.service-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--royal-blue), var(--royal-blue-accent));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    position: relative;
    top: -30px;
    box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

.service-icon i {
    font-size: 1.5rem;
    color: white;
}

.service-card h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    margin-top: -15px;
}

.service-card p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--royal-blue);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border: 2px solid var(--royal-blue);
    border-radius: 25px;
    background: transparent;
}

.service-link:hover {
    color: var(--text-white);
    background: var(--royal-blue);
    box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
}

/* ===== WHY CHOOSE US ===== */
.why-choose-us {
    padding: 100px 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
    gap: 2rem;
    width: 100%;
    max-width: 100%;
}

.feature-item {
    text-align: center;
    padding: 2rem;
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 1.8rem;
    color: white;
}

.feature-item h3 {
    color: #1a1a1a;
    margin-bottom: 1rem;
}

/* ===== PORTFOLIO ===== */
.portfolio {
    padding: 100px 0;
    background: #f8f9fa;
}

.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.filter-btn {
    padding: 10px 20px;
    border: 2px solid #ddd;
    background: white;
    color: #666;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn.active,
.filter-btn:hover {
    background: #25D366;
    color: white;
    border-color: #25D366;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
    gap: 2rem;
    width: 100%;
    max-width: 100%;
}

.portfolio-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-5px);
}

.portfolio-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.1);
}

.portfolio-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
}

.portfolio-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.portfolio-placeholder span {
    font-size: 1.2rem;
    font-weight: 600;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(37, 211, 102, 0.9);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    padding: 2rem;
    text-align: center;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-overlay h3 {
    margin-bottom: 0.5rem;
    color: white;
}

.portfolio-overlay p {
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.9);
}

.portfolio-link {
    color: white;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 8px 16px;
    border: 2px solid white;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.portfolio-link:hover {
    background: white;
    color: #25D366;
}

/* ===== ABOUT SECTION ===== */
.about {
    padding: 100px 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    color: #1a1a1a;
    margin-bottom: 1.5rem;
}

.about-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-item h3 {
    font-size: 2.5rem;
    color: #25D366;
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: #666;
    font-weight: 500;
}

.about-image {
    height: 400px;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
}

.image-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.image-placeholder span {
    font-size: 1.3rem;
    font-weight: 600;
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: 100px 0;
    background: var(--background-light);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-cards {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 1.5rem;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(30, 58, 138, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(30, 58, 138, 0.12);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--royal-blue), var(--royal-blue-accent));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 4px 15px rgba(30, 58, 138, 0.2);
}

.contact-icon i {
    color: var(--text-white);
    font-size: 1.4rem;
}

.contact-details h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.contact-details p {
    color: var(--text-secondary);
    margin: 0;
    font-weight: 500;
}

.contact-form {
    background: var(--white);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(30, 58, 138, 0.1);
    border: 1px solid var(--royal-blue-pale);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid var(--royal-blue-pale);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: inherit;
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--royal-blue);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-muted);
}

/* ===== FOOTER ===== */
.footer {
    background: linear-gradient(135deg, var(--royal-blue) 0%, var(--royal-blue-dark) 50%, var(--navy-complement) 100%);
    color: var(--text-white);
    padding: 80px 0 30px;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.2fr;
    gap: 4rem;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
}

.footer-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-logo {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.footer-logo-image {
    height: 50px;
    width: auto;
    object-fit: contain;
    filter: brightness(1.1);
}

.footer-logo h3 {
    color: var(--text-white);
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.footer-logo p {
    color: var(--light-accent);
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
}

.footer-section > p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin: 0;
    font-size: 0.95rem;
}

.footer-section h4 {
    color: var(--text-white);
    margin: 0 0 1.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--light-accent);
    border-radius: 1px;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.footer-section li {
    margin: 0;
}

.footer-section a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0;
}

.footer-section a:hover {
    color: var(--text-white);
    transform: translateX(5px);
}

.footer-section a::before {
    content: '→';
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    color: var(--light-accent);
}

.footer-section a:hover::before {
    opacity: 1;
    transform: translateX(0);
}

/* Contact Info Styling */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Footer Contact Info - Row Layout */
.footer-section .contact-info {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: flex-start;
}

.footer-section .contact-info p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    white-space: nowrap;
    flex-shrink: 0;
}

.contact-info p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.95rem;
    padding: 0.5rem 0;
    transition: all 0.3s ease;
}

.contact-info p:hover {
    color: var(--text-white);
    transform: translateX(3px);
}

.contact-info i {
    color: var(--light-accent);
    font-size: 1.1rem;
    width: 18px;
    text-align: center;
    flex-shrink: 0;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    text-align: center;
    position: relative;
    z-index: 1;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
    font-size: 0.9rem;
}

/* ===== WHATSAPP FLOATING BUTTON ===== */
.whatsapp-float {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.whatsapp-float a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #25D366, #128C7E);
    border-radius: 50%;
    color: white;
    font-size: 1.8rem;
    text-decoration: none;
    box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.whatsapp-float a:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(37, 211, 102, 0.6);
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    }
    50% {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.6), 0 0 0 10px rgba(37, 211, 102, 0.1);
    }
    100% {
        box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    }
}

/* ===== RESPONSIVE DESIGN ===== */

/* Extra Large Screens */
@media (max-width: 1400px) {
    .container {
        max-width: 1140px;
        padding: 0 20px;
    }

    .mega-menu {
        max-width: 1000px;
        margin: 0 20px;
    }

    .mega-menu-content {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Large Screens */
@media (max-width: 1200px) {
    .container {
        max-width: 960px;
        padding: 0 20px;
    }

    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
        gap: 2rem;
    }

    .hero-title {
        font-size: 3.5rem;
        line-height: 1.2;
    }

    .hero-content {
        padding: 0 20px;
    }
}

/* Medium Screens - Desktop (1024px) */
@media (max-width: 1024px) {
    .container {
        max-width: 100%;
        padding: 0 30px;
    }

    .mega-menu {
        max-width: 700px;
        margin: 0 20px;
    }

    .mega-menu-content {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero {
        min-height: 90vh;
        padding: 2rem 0;
    }

    .hero-title {
        font-size: 3.2rem;
        line-height: 1.2;
        margin-bottom: 1.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        padding: 0 20px;
    }

    .hero-content {
        max-width: 900px;
        padding: 0 20px;
    }

    .hero-buttons {
        gap: 1.5rem;
        margin-bottom: 2.5rem;
    }

    .hero-features {
        gap: 2.5rem;
        margin-top: 2.5rem;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .contact-content {
        display: grid;
        grid-template-columns: 1fr;
        gap: 3rem;
        align-items: start;
    }

    .contact-form {
        padding: 2.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
    }

    .footer-section {
        text-align: center;
    }

    .footer-section h4::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .social-links {
        justify-content: center;
    }

    .contact-info {
        align-items: center;
    }

    .services-overview,
    .contact,
    .about {
        padding: 80px 0;
    }

    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(320px, 100%), 1fr));
        gap: 2rem;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(280px, 100%), 1fr));
        gap: 2rem;
    }

    .portfolio-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
        gap: 2rem;
    }
}

/* Tablet Screens - 768px */
@media (max-width: 768px) {
    .container {
        max-width: 100%;
        padding: 0 25px;
    }

    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mega-menu {
        display: none;
    }

    /* Hide the Get Quote button in navigation on mobile */
    .nav-content .btn-primary {
        display: none;
    }

    /* Marquee effect for entire top bar on mobile/tablet */
    .top-bar-content {
        overflow: hidden;
        position: relative;
        width: 100%;
        animation: marquee 30s linear infinite;
        white-space: nowrap;
        display: flex !important;
        flex-direction: row !important;
        gap: 4rem;
        flex-wrap: nowrap !important;
        min-width: max-content;
        padding-right: 4rem;
        align-items: center;
    }

    .top-bar-content:hover {
        animation-play-state: paused;
    }

    .contact-info {
        display: flex !important;
        flex-direction: row !important;
        gap: 3rem;
        flex-wrap: nowrap !important;
        flex-shrink: 0;
        align-items: center;
    }

    .social-links {
        display: flex !important;
        flex-direction: row !important;
        gap: 1.5rem;
        flex-wrap: nowrap !important;
        flex-shrink: 0;
        align-items: center;
    }

    .contact-info a,
    .contact-info span,
    .social-links a {
        flex-shrink: 0;
        white-space: nowrap;
    }

    @keyframes marquee {
        0% {
            transform: translateX(100%);
        }
        100% {
            transform: translateX(-100%);
        }
    }

    .hero {
        margin-top: 110px;
        padding: 2rem 0;
        min-height: calc(100vh - 110px);
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .hero-content {
        position: relative;
        z-index: 100;
        max-width: 100%;
        padding: 2rem 25px;
        width: 100%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    .hero-title {
        font-size: clamp(2.2rem, 6vw, 2.8rem);
        line-height: 1.2;
        margin-bottom: 1.5rem;
        word-wrap: break-word;
        max-width: 100%;
    }

    .hero-subtitle {
        font-size: clamp(1rem, 3vw, 1.15rem);
        margin-bottom: 2rem;
        padding: 0 15px;
        line-height: 1.5;
        max-width: 100%;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1.25rem;
        width: 100%;
        margin-bottom: 2.5rem;
        max-width: 100%;
        box-sizing: border-box;
    }

    .hero-buttons .btn-primary,
    .hero-buttons .btn-secondary {
        width: 100%;
        max-width: 320px;
        justify-content: center;
        display: flex;
        align-items: center;
        padding: 14px 28px;
        font-size: 1rem;
        box-sizing: border-box;
        min-height: 48px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .hero-features {
        flex-direction: column;
        gap: 1.75rem;
        margin-top: 2rem;
        width: 100%;
        max-width: 100%;
        align-items: center;
        justify-content: center;
    }

    /* Slider responsive styles */
    .slider-nav {
        padding: 0 20px;
    }

    .nav-btn {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .slider-dots {
        bottom: 25px;
    }

    .dot {
        width: 11px;
        height: 11px;
    }

    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(350px, 100%), 1fr));
        gap: 2rem;
        max-width: 100%;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(300px, 100%), 1fr));
        gap: 2rem;
    }

    .portfolio-grid {
        grid-template-columns: repeat(auto-fit, minmax(min(320px, 100%), 1fr));
        gap: 2rem;
    }

    .service-card {
        margin: 0 auto;
        max-width: 100%;
        width: 100%;
    }

    .about-stats {
        justify-content: center;
        flex-wrap: wrap;
        gap: 1.5rem;
    }

    .contact-cards {
        gap: 1.75rem;
    }

    .contact-item {
        padding: 1.5rem;
    }

    .contact-form {
        padding: 2.25rem;
    }

    .footer {
        padding: 60px 0 25px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .footer-section {
        text-align: center;
    }

    .footer-section h4::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-logo {
        align-items: center;
    }

    .social-links {
        justify-content: center;
    }

    .contact-info {
        align-items: center;
    }

    .contact-info p {
        justify-content: center;
    }

    /* Footer contact info responsive - tablet */
    .footer-section .contact-info {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .footer-section .contact-info p {
        justify-content: center;
        white-space: normal;
    }

    .top-bar {
        padding: 8px 0;
        min-height: 35px;
    }

    .top-bar-content {
        flex-direction: row;
        gap: 1rem;
        text-align: center;
        padding: 0.5rem 0;
        justify-content: space-between;
        align-items: center;
    }

    .contact-info {
        justify-content: flex-start;
        gap: 1rem;
        flex-wrap: nowrap;
        flex-direction: row !important;
        display: flex !important;
        flex: 1;
    }

    .contact-link,
    .contact-hours {
        font-size: 0.8rem;
        white-space: nowrap;
    }

    .social-links {
        justify-content: flex-end;
        gap: 0.75rem;
        flex-shrink: 0;
    }

    .social-links a {
        font-size: 1rem;
        padding: 2px;
    }

    .services-overview,
    .contact,
    .about {
        padding: 70px 0;
    }

    .section-header {
        margin-bottom: 3.5rem;
    }

    .section-header h2 {
        font-size: 2.2rem;
        line-height: 1.2;
    }

    .section-header p {
        font-size: 1.05rem;
        padding: 0 15px;
    }
}

/* Mobile Screens - 480px and below */
@media (max-width: 480px) {
    .container {
        padding: 0 20px;
        max-width: 100%;
    }

    /* Ensure all buttons are touch-friendly */
    .btn-primary, .btn-secondary {
        min-height: 48px;
        min-width: 48px;
        padding: 14px 24px;
        font-size: 1rem;
        box-sizing: border-box;
    }

    /* Fix any potential button overflow */
    .hero-buttons .btn-primary,
    .hero-buttons .btn-secondary {
        width: 100%;
        max-width: 300px;
        justify-content: center;
        margin: 0 auto;
        padding: 16px 28px;
        font-size: 1rem;
        box-sizing: border-box;
        min-height: 48px;
    }

    h1 { font-size: clamp(1.8rem, 5vw, 2.2rem); }
    h2 { font-size: clamp(1.6rem, 4vw, 1.9rem); }
    h3 { font-size: clamp(1.3rem, 3vw, 1.5rem); }

    .hero {
        padding: 1.5rem 0;
        min-height: calc(100vh - 90px);
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 90px;
    }

    .hero-content {
        position: relative;
        z-index: 100;
        padding: 4rem 20px;
        width: 100%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        max-width: 100%;
    }

    .hero-title {
        font-size: clamp(1.8rem, 6vw, 2.3rem);
        line-height: 1.2;
        margin-bottom: 1.25rem;
        word-wrap: break-word;
        max-width: 100%;
    }

    .hero-subtitle {
        font-size: clamp(0.9rem, 3vw, 1.1rem);
        margin-bottom: 2rem;
        padding: 0 10px;
        line-height: 1.5;
        max-width: 100%;
    }

    .hero-buttons {
        gap: 1.5rem;
        margin-bottom: 2.5rem;
        flex-direction: column;
        width: 100%;
        max-width: 100%;
        align-items: center;
        justify-content: center;
    }

    .hero-features {
        gap: 1.5rem;
        margin-top: 2rem;
        flex-direction: column;
        width: 100%;
        max-width: 100%;
        align-items: center;
        justify-content: center;
    }

    /* Logo responsive styles */
    .logo {
        flex-direction: row;
        gap: 0.75rem;
        text-align: left;
    }

    .logo-image {
        height: 40px;
    }

    .logo-text h1 {
        font-size: 1.6rem;
    }

    .logo-text span {
        font-size: 0.85rem;
    }

    .footer-logo-image {
        height: 35px;
    }

    /* Slider mobile improvements */
    .slider-nav {
        padding: 0 15px;
    }

    .nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .slider-dots {
        bottom: 20px;
    }

    .dot {
        width: 10px;
        height: 10px;
    }

    /* Services responsive */
    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card {
        max-width: 100%;
        width: 100%;
    }

    .service-content {
        padding: 1.75rem;
    }

    .service-icon {
        width: 55px;
        height: 55px;
        top: -27px;
    }

    .service-icon i {
        font-size: 1.3rem;
    }

    /* Features grid mobile */
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    /* Portfolio grid mobile */
    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    /* Contact responsive */
    .contact-form {
        padding: 1.75rem;
    }

    .contact-item {
        padding: 1.25rem;
        gap: 1.25rem;
    }

    .contact-icon {
        width: 55px;
        height: 55px;
    }

    .contact-icon i {
        font-size: 1.3rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 14px 18px;
        font-size: 1rem;
    }

    .about-stats {
        flex-direction: column;
        gap: 1.5rem;
    }

    /* Portfolio responsive */
    .portfolio-filters {
        flex-wrap: wrap;
        gap: 0.75rem;
        justify-content: center;
    }

    .filter-btn {
        padding: 10px 18px;
        font-size: 0.95rem;
    }

    .whatsapp-float {
        bottom: 25px;
        right: 25px;
    }

    .whatsapp-float a {
        width: 55px;
        height: 55px;
        font-size: 1.6rem;
    }

    .services-overview,
    .contact,
    .about {
        padding: 50px 0;
    }

    .section-header {
        margin-bottom: 3rem;
    }

    .section-header h2 {
        font-size: 1.9rem;
        line-height: 1.2;
    }

    .section-header p {
        font-size: 1.05rem;
        padding: 0 10px;
    }

    /* Top bar mobile improvements with marquee */
    .top-bar {
        padding: 8px 0;
        min-height: 35px;
    }

    .top-bar-content {
        overflow: hidden;
        position: relative;
        width: 100%;
        animation: marquee 25s linear infinite;
        white-space: nowrap;
        display: flex !important;
        flex-direction: row !important;
        gap: 3rem;
        flex-wrap: nowrap !important;
        min-width: max-content;
        padding-right: 3rem;
        align-items: center;
        padding: 0.25rem 0;
    }

    .top-bar-content:hover {
        animation-play-state: paused;
    }

    .contact-info {
        display: flex !important;
        flex-direction: row !important;
        gap: 2.5rem;
        flex-wrap: nowrap !important;
        flex-shrink: 0;
        align-items: center;
    }

    .social-links {
        display: flex !important;
        flex-direction: row !important;
        gap: 1rem;
        flex-wrap: nowrap !important;
        flex-shrink: 0;
        align-items: center;
    }

    .contact-info a,
    .contact-info span,
    .social-links a {
        flex-shrink: 0;
        white-space: nowrap;
    }

    .contact-link,
    .contact-hours {
        font-size: 0.8rem;
        text-align: left;
        white-space: nowrap;
    }

    .social-links {
        gap: 0.5rem;
        justify-content: flex-end;
        flex-shrink: 0;
    }

    .social-links a {
        font-size: 0.9rem;
        padding: 2px;
    }

    /* Footer mobile styles */
    .footer {
        padding: 50px 0 20px;
    }

    .footer-content {
        gap: 2.5rem;
    }

    .footer-section h4 {
        font-size: 1.1rem;
    }

    .social-links a {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .footer-logo-image {
        height: 40px;
    }

    /* Footer contact info responsive - mobile */
    .footer-section .contact-info {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .footer-section .contact-info p {
        justify-content: center;
        white-space: normal;
        text-align: center;
    }
}

/* Extra Small Screens - 360px and below */
@media (max-width: 360px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 1.5rem 0;
        min-height: calc(100vh - 80px);
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 80px;
    }

    .hero-content {
        padding: 1rem 15px;
        width: 100%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        max-width: 100%;
    }

    .hero-title {
        font-size: clamp(1.5rem, 7vw, 2rem);
        line-height: 1.2;
        margin-bottom: 1rem;
        word-wrap: break-word;
        max-width: 100%;
    }

    .hero-subtitle {
        font-size: clamp(0.85rem, 3.5vw, 1rem);
        margin-bottom: 1.75rem;
        padding: 0 5px;
        line-height: 1.5;
        max-width: 100%;
    }

    .hero-buttons {
        gap: 1.25rem;
        margin-bottom: 2rem;
        flex-direction: column;
        width: 100%;
        max-width: 100%;
        align-items: center;
        justify-content: center;
    }

    .hero-buttons .btn-primary,
    .hero-buttons .btn-secondary {
        padding: 12px 24px;
        font-size: 0.95rem;
        max-width: 280px;
        width: 100%;
        box-sizing: border-box;
        min-height: 44px;
    }

    .hero-features {
        gap: 1.25rem;
        margin-top: 1.5rem;
        flex-direction: column;
        width: 100%;
        max-width: 100%;
        align-items: center;
        justify-content: center;
    }

    /* Logo extra small */
    .logo-image {
        height: 35px;
    }

    .logo-text h1 {
        font-size: 1.4rem;
    }

    .logo-text span {
        font-size: 0.8rem;
    }

    /* Typography adjustments */
    h1 { font-size: 2rem; }
    h2 { font-size: 1.7rem; }
    h3 { font-size: 1.3rem; }

    .section-header h2 {
        font-size: 1.7rem;
        line-height: 1.2;
    }

    .section-header p {
        font-size: 1rem;
        padding: 0 5px;
    }

    /* Services extra small */
    .service-content {
        padding: 1.5rem;
    }

    .service-icon {
        width: 50px;
        height: 50px;
        top: -25px;
    }

    .service-icon i {
        font-size: 1.2rem;
    }

    /* Contact extra small */
    .contact-form {
        padding: 1.5rem;
    }

    .contact-item {
        padding: 1rem;
        gap: 1rem;
    }

    .contact-icon {
        width: 50px;
        height: 50px;
    }

    .contact-icon i {
        font-size: 1.2rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 12px 16px;
        font-size: 0.95rem;
    }

    /* WhatsApp float extra small */
    .whatsapp-float {
        bottom: 20px;
        right: 20px;
    }

    .whatsapp-float a {
        width: 50px;
        height: 50px;
        font-size: 1.4rem;
    }

    /* Sections padding */
    .services-overview,
    .contact,
    .about {
        padding: 40px 0;
    }

    .section-header {
        margin-bottom: 2.5rem;
    }

    /* Top bar extra small screens with marquee */
    .top-bar {
        padding: 6px 0;
        min-height: 30px;
    }

    .top-bar-content {
        overflow: hidden;
        position: relative;
        width: 100%;
        animation: marquee 22s linear infinite;
        white-space: nowrap;
        display: flex !important;
        flex-direction: row !important;
        gap: 2.5rem;
        flex-wrap: nowrap !important;
        min-width: max-content;
        padding-right: 2.5rem;
        align-items: center;
        padding: 0.125rem 0;
    }

    .top-bar-content:hover {
        animation-play-state: paused;
    }

    .contact-info {
        display: flex !important;
        flex-direction: row !important;
        gap: 2rem;
        flex-wrap: nowrap !important;
        flex-shrink: 0;
        align-items: center;
    }

    .social-links {
        display: flex !important;
        flex-direction: row !important;
        gap: 0.75rem;
        flex-wrap: nowrap !important;
        flex-shrink: 0;
        align-items: center;
    }

    .contact-info a,
    .contact-info span,
    .social-links a {
        flex-shrink: 0;
        white-space: nowrap;
    }

    .contact-link,
    .contact-hours {
        font-size: 0.7rem;
        text-align: left;
        white-space: nowrap;
    }

    .social-links {
        gap: 0.25rem;
        justify-content: flex-end;
        flex-shrink: 0;
    }

    .social-links a {
        font-size: 0.8rem;
        padding: 1px;
    }

    /* Portfolio filters extra small */
    .filter-btn {
        padding: 8px 14px;
        font-size: 0.85rem;
    }

    /* Footer adjustments */
    .footer {
        padding: 40px 0 15px;
    }

    .footer-content {
        gap: 2rem;
    }

    .footer-section h4 {
        font-size: 1rem;
    }

    .footer-logo-image {
        height: 35px;
    }

    .social-links a {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    /* Footer contact info responsive - extra small */
    .footer-section .contact-info {
        flex-direction: column;
        gap: 0.75rem;
        align-items: center;
    }

    .footer-section .contact-info p {
        justify-content: center;
        white-space: normal;
        text-align: center;
        font-size: 0.85rem;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden { display: none; }
.visible { display: block; }

/* ===== ANIMATIONS ===== */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.slide-up {
    transform: translateY(30px);
    opacity: 0;
    animation: slideUp 0.6s ease forwards;
}

@keyframes slideUp {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.scale-in {
    transform: scale(0.9);
    opacity: 0;
    animation: scaleIn 0.6s ease forwards;
}

@keyframes scaleIn {
    to {
        transform: scale(1);
        opacity: 1;
    }
}
