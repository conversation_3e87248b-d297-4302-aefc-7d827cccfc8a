/* ===== CUSTOM ANIMATIONS ===== */

/* Floating Animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.float {
    animation: float 3s ease-in-out infinite;
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -15px, 0);
    }
    70% {
        transform: translate3d(0, -7px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.bounce {
    animation: bounce 2s infinite;
}

/* Shake Animation */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

.shake {
    animation: shake 0.8s ease-in-out;
}

/* Glow Animation */
@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(37, 211, 102, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(37, 211, 102, 0.8), 0 0 30px rgba(37, 211, 102, 0.6);
    }
}

.glow {
    animation: glow 2s ease-in-out infinite;
}

/* Rotate Animation */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.rotate {
    animation: rotate 2s linear infinite;
}

/* Slide In Animations */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.slide-in-down {
    animation: slideInDown 0.6s ease-out;
}

/* Zoom Animations */
@keyframes zoomIn {
    from {
        transform: scale(0);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.zoom-in {
    animation: zoomIn 0.6s ease-out;
}

@keyframes zoomOut {
    from {
        transform: scale(1);
        opacity: 1;
    }
    to {
        transform: scale(0);
        opacity: 0;
    }
}

.zoom-out {
    animation: zoomOut 0.6s ease-out;
}

/* Flip Animations */
@keyframes flipInX {
    from {
        transform: perspective(400px) rotateX(90deg);
        opacity: 0;
    }
    40% {
        transform: perspective(400px) rotateX(-20deg);
    }
    60% {
        transform: perspective(400px) rotateX(10deg);
        opacity: 1;
    }
    80% {
        transform: perspective(400px) rotateX(-5deg);
    }
    to {
        transform: perspective(400px) rotateX(0deg);
        opacity: 1;
    }
}

.flip-in-x {
    animation: flipInX 0.8s ease-out;
}

@keyframes flipInY {
    from {
        transform: perspective(400px) rotateY(90deg);
        opacity: 0;
    }
    40% {
        transform: perspective(400px) rotateY(-20deg);
    }
    60% {
        transform: perspective(400px) rotateY(10deg);
        opacity: 1;
    }
    80% {
        transform: perspective(400px) rotateY(-5deg);
    }
    to {
        transform: perspective(400px) rotateY(0deg);
        opacity: 1;
    }
}

.flip-in-y {
    animation: flipInY 0.8s ease-out;
}

/* Pulse Animation */
@keyframes pulseScale {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.pulse-scale {
    animation: pulseScale 2s ease-in-out infinite;
}

/* Heartbeat Animation */
@keyframes heartbeat {
    0%, 50%, 100% {
        transform: scale(1);
    }
    5%, 45% {
        transform: scale(1.1);
    }
    10%, 40% {
        transform: scale(1.05);
    }
    15%, 35% {
        transform: scale(1.15);
    }
    20%, 30% {
        transform: scale(1.1);
    }
    25% {
        transform: scale(1.2);
    }
}

.heartbeat {
    animation: heartbeat 2s ease-in-out infinite;
}

/* Wobble Animation */
@keyframes wobble {
    0%, 100% {
        transform: translateX(0%);
    }
    15% {
        transform: translateX(-25%) rotate(-5deg);
    }
    30% {
        transform: translateX(20%) rotate(3deg);
    }
    45% {
        transform: translateX(-15%) rotate(-3deg);
    }
    60% {
        transform: translateX(10%) rotate(2deg);
    }
    75% {
        transform: translateX(-5%) rotate(-1deg);
    }
}

.wobble {
    animation: wobble 1s ease-in-out;
}

/* Typing Animation */
@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        border-color: transparent;
    }
    51%, 100% {
        border-color: #25D366;
    }
}

.typing-effect {
    overflow: hidden;
    border-right: 2px solid #25D366;
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink 0.75s step-end infinite;
}

/* Gradient Animation */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.gradient-animation {
    background: linear-gradient(-45deg, #667eea, #764ba2, #25D366, #128C7E);
    background-size: 400% 400%;
    animation: gradientShift 4s ease infinite;
}

/* Loading Spinner */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #25D366;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Stagger Animation Delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }

/* Animation Utilities */
.animate-once {
    animation-iteration-count: 1;
}

.animate-infinite {
    animation-iteration-count: infinite;
}

.animate-slow {
    animation-duration: 3s;
}

.animate-fast {
    animation-duration: 0.5s;
}
